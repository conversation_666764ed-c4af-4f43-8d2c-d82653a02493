#!/usr/bin/env python3
"""
测试 distribute_coupons_by_event 方法的功能

这个测试脚本演示了如何使用新实现的基于事件的优惠券发放功能
"""

from datetime import datetime, timedelta
from app.events.models import UserEvent, UserEventAction
from app.service.coupon import CouponService

def test_event_to_channel_mapping():
    """测试事件到发放渠道的映射"""
    print("=== 测试事件到发放渠道的映射 ===")
    
    # 测试用户注册事件
    register_event = UserEvent(
        action=UserEventAction.REGISTERED,
        user_id=123,
        source="test"
    )
    channel = CouponService._map_event_to_distribution_channel(register_event)
    print(f"用户注册事件 -> 发放渠道: {channel}")
    assert channel == "new_user", f"期望 'new_user'，实际 '{channel}'"
    
    # 测试浏览活动事件
    view_event = UserEvent(
        action=UserEventAction.VIEWED,
        user_id=123,
        source="test"
    )
    channel = CouponService._map_event_to_distribution_channel(view_event)
    print(f"浏览活动事件 -> 发放渠道: {channel}")
    assert channel == "view_activity", f"期望 'view_activity'，实际 '{channel}'"
    
    # 测试分享活动事件
    share_event = UserEvent(
        action=UserEventAction.SHARED,
        user_id=123,
        source="test"
    )
    channel = CouponService._map_event_to_distribution_channel(share_event)
    print(f"分享活动事件 -> 发放渠道: {channel}")
    assert channel == "share_activity", f"期望 'share_activity'，实际 '{channel}'"
    
    # 测试不支持的事件
    login_event = UserEvent(
        action=UserEventAction.LOGIN,
        user_id=123,
        source="test"
    )
    channel = CouponService._map_event_to_distribution_channel(login_event)
    print(f"登录事件 -> 发放渠道: {channel}")
    assert channel == "", f"期望 ''，实际 '{channel}'"
    
    print("✅ 事件到发放渠道的映射测试通过\n")

def simulate_coupon_distribution():
    """模拟优惠券发放流程"""
    print("=== 模拟优惠券发放流程 ===")
    
    # 模拟不同事件的发放结果
    test_cases = [
        {
            "event": UserEvent(action=UserEventAction.REGISTERED, user_id=1001, source="test"),
            "description": "新用户注册"
        },
        {
            "event": UserEvent(action=UserEventAction.VIEWED, user_id=1002, source="test"),
            "description": "浏览活动"
        },
        {
            "event": UserEvent(action=UserEventAction.SHARED, user_id=1003, source="test"),
            "description": "分享活动"
        },
        {
            "event": UserEvent(action=UserEventAction.LOGIN, user_id=1004, source="test"),
            "description": "用户登录（不支持）"
        }
    ]
    
    for case in test_cases:
        print(f"测试场景: {case['description']}")
        print(f"事件动作: {case['event'].action}")
        
        # 映射到发放渠道
        channel = CouponService._map_event_to_distribution_channel(case['event'])
        print(f"映射的发放渠道: {channel if channel else '不支持'}")
        
        if channel:
            print(f"✅ 该事件支持优惠券发放，渠道: {channel}")
        else:
            print(f"❌ 该事件不支持优惠券发放")
        print("-" * 50)
    
    print("✅ 优惠券发放流程模拟完成\n")

def demonstrate_usage():
    """演示使用方法"""
    print("=== 使用方法演示 ===")
    
    print("""
    使用 distribute_coupons_by_event 方法的步骤：
    
    1. 创建事件对象：
       event = UserEvent(
           action=UserEventAction.REGISTERED,  # 或 VIEWED, SHARED
           user_id=123,
           source="your_service_name"
       )
    
    2. 调用发放方法：
       result = CouponService.distribute_coupons_by_event(
           session=db_session,
           user_id=123,
           event=event
       )
    
    3. 处理返回结果：
       if result['success']:
           print(f"成功发放 {result['distributed_count']} 张优惠券")
       else:
           print(f"发放失败: {result['message']}")
    
    支持的事件动作和对应的发放渠道：
    - UserEventAction.REGISTERED  -> "new_user"
    - UserEventAction.VIEWED      -> "view_activity"  
    - UserEventAction.SHARED      -> "share_activity"
    
    发放约束检查包括：
    - 批次状态和有效期
    - 发放渠道匹配
    - 周期发放数量限制
    - 用户周期领取数量限制
    - 用户领取时间范围
    - 用户是否已拥有该批次优惠券
    """)

if __name__ == "__main__":
    print("🚀 开始测试 distribute_coupons_by_event 方法")
    print("=" * 60)
    
    try:
        # 运行测试
        test_event_to_channel_mapping()
        simulate_coupon_distribution()
        demonstrate_usage()
        
        print("🎉 所有测试完成！")
        print("\n📝 方法实现总结：")
        print("✅ 实现了 distribute_coupons_by_event 方法")
        print("✅ 支持用户注册、浏览活动、分享活动事件")
        print("✅ 完整的发放和领取约束检查")
        print("✅ 详细的错误处理和日志记录")
        print("✅ 清晰的返回结果结构")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
