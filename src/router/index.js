import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [{
    path: '',
    component: Layout,
    redirect: '/dashboard',
    meta: {
      ename: ['dashboard'],
      icon: 'dashboard'
    },
    name: 'Dashboard',
    children: [{
      path: 'dashboard',
      component: () => import('@/views/dashboard/index'),
      meta: {
        title: '首页',
        ename: ['dashboard'],
        noCache: true
      }
    }]
  },

  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    name: 'Page404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  // 404 page must be placed at the end !!!
  // {
  //   path: '*',
  //   redirect: '/404',
  //   hidden: true,
  //   meta: {
  //     ename: ['404']
  //   }
  // },
  {
    path: '/401',
    name: 'Page401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  }
]

export const asyncRoutes = [

  // // CronTime演示
  // {
  //   path: '/cron-time',
  //   component: Layout,
  //   redirect: '/cron-time/demo',
  //   name: 'CronTimeDemo',
  //   meta: {
  //     title: 'Cron时间组件',
  //     icon: 'el-icon-time',
  //     role: ['admin']
  //   },
  //   children: [
  //     {
  //       path: 'demo',
  //       component: () => import('@/views/cron-time-demo'),
  //       name: 'CronTimeComponentDemo',
  //       meta: {
  //         title: '组件演示',
  //         role: ['admin'],
  //         noCache: true
  //       }
  //     }
  //   ]
  // },

  // 用户管理
  {
    path: '/user',
    component: Layout,
    name: 'UserManagement',
    meta: {
      title: '用户管理',
      icon: 'peoples',
      role: ['admin'],
      permission: ['user:manage']
    },
    children: [
      {
        path: 'personal-user',
        component: () => import('@/views/user/personal-user.vue'),
        name: 'PersonalUserManagement',
        meta: {
          title: '个人用户',
          role: ['admin'],
          permission: ['user:personal:read'],
          noCache: true
        }
      },
      {
        path: 'enterprise-user',
        component: () => import('@/views/user/enterprise-user.vue'),
        name: 'EnterpriseUserManagement',
        meta: {
          title: '企业用户',
          role: ['admin'],
          permission: ['user:enterprise:read'],
          noCache: true
        }
      }
    ]
  },

  // 资金账户
  {
    path: '/account',
    component: Layout,
    name: 'AccountManagement',
    meta: {
      title: '帐户管理',
      icon: 'money',
      role: ['admin'],
      permission: ['account:manage']
    },
    children: [
      {
        path: 'personal-account',
        component: () => import('@/views/account/personal-account.vue'),
        name: 'PersonalAccountManagement',
        meta: {
          title: '个人账户',
          role: ['admin'],
          permission: ['account:personal:read'],
          noCache: true
        }
      },
      {
        path: 'enterprise-account',
        component: () => import('@/views/account/enterprise-account.vue'),
        name: 'EnterpriseAccountManagement',
        meta: {
          title: '企业账户',
          role: ['admin'],
          permission: ['account:enterprise:read'],
          noCache: true
        }
      }
    ]
  },

  // 库存管理
  {
    path: '/content',
    component: Layout,
    name: 'ContentManagement',
    meta: {
      title: '内容管理',
      icon: 'shopping',
      role: ['admin'],
      permission: ['content:manage']
    },
    children: [
      {
        path: '/content/article',
        component: () => import('@/views/content/article.vue'),
        name: 'Article',
        meta: {
          title: '文章',
          role: ['admin'],
          permission: ['article:read'],
          noCache: true
        }
      },
      {
        path: '/content/dish',
        component: () => import('@/views/content/dish.vue'),
        name: 'Dish',
        meta: {
          title: '菜品',
          role: ['admin'],
          permission: ['dish:read'],
          noCache: true
        }
      },
      {
        path: 'menu-list',
        component: () => import('@/views/menu/menu-list'),
        name: 'MenuList',
        meta: {
          title: '菜单管理',
          role: ['admin'],
          permission: ['menu:read'],
          noCache: true
        }
      },
    ]
  },

  // 产品服务
  {
    path: '/product',
    component: Layout,
    name: 'ProductManagement',
    meta: {
      title: '产品管理',
      icon: 'stock',
      role: ['admin'],
      permission: ['product:manage']
    },
    children: [
      {
        path: '/category/list',
        component: () => import('@/views/product/category-list'),
        name: 'CategoryList',
        meta: {
          title: '产品分类',
          role: ['admin'],
          permission: ['category:read'],
          noCache: true
        }
      },
      {
        path: '/product/list',
        component: () => import('@/views/product/product-list'),
        name: 'ProductList',
        meta: {
          title: '产品列表',
          role: ['admin'],
          permission: ['product:read'],
          noCache: true
        }
      }
    ]
  },

  // 设置管理
  {
    path: '/setting',
    component: Layout,
    name: 'SettingManagement',
    meta: {
      title: '设置管理',
      icon: 'dict',
      role: ['admin'],
      permission: ['setting:manage']
    },
    children: [
      {
        path: 'rule',
        component: () => import('@/views/setting/booking-rule/booking-rule-list.vue'),
        name: 'Rule',
        meta: {
          title: '预订规则',
          role: ['admin'],
          permission: ['rule:read'],
          noCache: true
        }
      },
      {
        path: 'pricing-strategy',
        component: () => import('@/views/setting/pricing-strategy/pricing-strategy-list.vue'),
        name: 'PricingStrategy',
        meta: {
          title: '价格策略',
          role: ['admin'],
          permission: ['pricing:read'],
          noCache: true
        }
      },
      {
        path: 'gift-strategy',
        component: () => import('@/views/gift/gift-strategy-list.vue'),
        name: 'GiftStrategyRule',
        meta: {
          title: '赠送策略',
          role: ['admin'],
          permission: ['gift:read'],
          noCache: true
        }
      },
    ]
  },

  // 优惠券
  {
    path: '/coupon',
    component: Layout,
    name: 'CouponManagement',
    meta: {
      title: '优惠券',
      icon: 'invoice',
      role: ['admin'],
      permission: ['coupon:manage']
    },
    children: [
      {
        path: 'coupon-list',
        component: () => import('@/views/coupon/coupon-list'),
        name: 'CouponList',
        meta: {
          title: '优惠券定义',
          role: ['admin'],
          permission: ['coupon:read'],
          noCache: true
        }
      },
      {
        path: 'batch-list',
        component: () => import('@/views/coupon/coupon-batch-list.vue'),
        name: 'CouponBatchList',
        meta: {
          title: '优惠券生成',
          role: ['admin'],
          permission: ['coupon:read'],
          noCache: true
        }
      },
      {
        path: 'coupon-usage-list',
        component: () => import('@/views/coupon/coupon-usage/coupon-usage-list.vue'),
        name: 'CouponUsageList',
        meta: {
          title: '已发优惠券',
          role: ['admin'],
          permission: ['coupon:usage:read'],
          noCache: true
        }
      },
      {
        path: 'distribution-rule',
        component: () => import('@/views/coupon/coupon-distribution-rule.vue'),
        name: 'CouponDistributionRule',
        meta: {
          title: '发券规则管理',
          role: ['admin'],
          permission: ['coupon:distribution:manage'],
          noCache: true
        }
      },
      {
        path: 'distribution-rule-test',
        component: () => import('@/views/coupon/coupon-distribution-rule-test.vue'),
        name: 'CouponDistributionRuleTest',
        meta: {
          title: '发券规则测试',
          role: ['admin'],
          permission: ['coupon:distribution:manage'],
          noCache: true
        }
      },
      {
        path: 'debug-api-test',
        component: () => import('@/views/coupon/debug-api-test.vue'),
        name: 'DebugApiTest',
        meta: {
          title: 'API调试测试',
          role: ['admin'],
          permission: ['coupon:distribution:manage'],
          noCache: true
        }
      },
    ]
  },

  // 订单记录
  {
    path: '/order',
    component: Layout,
    name: 'OrderManagement',
    meta: {
      title: '订单记录',
      icon: 'list',
      role: ['admin'],
      permission: ['order:manage']
    },
    children: [
      {
        path: 'reservation-record',
        component: () => import('@/views/order/reservation-record'),
        name: 'ReservationRecord',
        meta: {
          title: '自助餐订单',
          role: ['admin'],
          permission: ['order:reservation:read'],
          noCache: true
        }
      },
      {
        path: 'reservation-order-record',
        component: () => import('@/views/order/reservation-order-record'),
        name: 'ReservationOrderRecord',
        meta: {
          title: '商务餐订单',
          role: ['admin'],
          permission: ['order:reservation:read'],
          noCache: true
        }
      },
    ]
  },

  // 报表管理
  {
    path: '/statistic',
    component: Layout,
    name: 'FinanceManagement',
    meta: {
      title: '报表统计',
      icon: 'chart',
      role: ['admin'],
      permission: ['statistic:manage']
    },
    children: [
      {
        path: 'reservation-statistic',
        component: () => import('@/views/statistic/reservation-statistic.vue'),
        name: 'ReservationStatistic',
        meta: {
          title: '自助餐统计',
          role: ['admin'],
          permission: ['statistic:reservation:read'],
          noCache: true
        }
      },
      {
        path: 'reservation-order-statistic',
        component: () => import('@/views/statistic/reservation-order-statistic.vue'),
        name: 'ReservationOrderStatistic',
        meta: {
          title: '商务餐统计',
          role: ['admin'],
          permission: ['statistic:reservation:read'],
          noCache: true
        }
      },
    ]
  },

  // 系统管理
  {
    path: '/system-setting',
    component: Layout,
    name: 'SystemSetting',
    meta: {
      title: '系统管理',
      icon: 'table',
      role: ['admin'],
      permission: ['system:manage']
    },
    children: [
      {
        path: 'admin',
        component: () => import('@/views/system/admin-list'),
        name: 'Admin',
        meta: {
          title: '管理员管理',
          role: ['admin'],
          permission: ['admin:read'],
          noCache: true
        }
      },
      {
        path: 'role',
        component: () => import('@/views/system/role-list'),
        name: 'Role',
        meta: {
          title: '角色管理',
          role: ['admin'],
          permission: ['role:read'],
          noCache: true
        }
      },
      {
        path: 'permission',
        component: () => import('@/views/system/permission-list'),
        name: 'Permission',
        meta: {
          title: '权限管理',
          role: ['admin'],
          permission: ['permission:read'],
          noCache: true
        }
      }
    ]
  },

  // 个人信息
  {
    path: '/profile',
    component: Layout,
    meta: {
      role: ['admin'],
      permission: ['profile:manage'],
      title: '个人信息',
      icon: 'userMe'
    },
    children: [{
      path: 'user-info',
      component: () => import('@/views/profile/info.vue'),
      name: 'UserInfo',
      meta: {
        role: ['admin'],
        permission: ['profile:read'],
        title: '个人信息',
        noCache: true
      }
    }]
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]

const createRouter = () =>
  new Router({
    mode: 'history', // require service support
    scrollBehavior: () => ({
      y: 0
    }),
    routes: constantRoutes
  })

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
