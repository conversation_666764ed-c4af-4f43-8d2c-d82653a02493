# 发券规则管理功能故障排除指南

## 问题描述

在实现发券规则管理功能时遇到了以下错误：

1. `TypeError: Object(...) is not a function` 错误
2. `422 Unprocessable Entity` 错误：参数验证失败

## 问题原因

1. **错误的导入方式**: 最初使用了 `import { request }` 而不是 `import { requestApi }`
2. **错误的API调用方式**: 使用了直接传递 `url` 和 `method` 的方式，而项目中使用的是 `name` 属性方式
3. **异步调用方式不一致**: 使用了 `async/await` 而项目中使用的是 `.then()/.catch()`
4. **参数验证问题**: 传递空字符串导致后端参数验证失败

## 解决方案

### 1. 修正导入语句

```javascript
// 错误的方式
import { request } from '@/utils/request'

// 正确的方式
import { requestApi } from '@/utils/request'
```

### 2. 修正API调用方式

```javascript
// 错误的方式
const response = await requestApi({
  url: couponApi.getDistributionRules.url,
  method: couponApi.getDistributionRules.method,
  params
})

// 正确的方式
requestApi({
  name: 'getDistributionRules',
  data: params
})
  .then((response) => {
    // 处理响应
  })
  .catch((error) => {
    // 处理错误
  })
```

### 3. 统一异步调用模式

项目中使用 `.then()/.catch()` 模式而不是 `async/await`，已统一修改。

### 4. 修复参数验证问题

```javascript
// 修复前：直接传递所有参数（包括空字符串）
const params = {
  keyword: this.searchForm.keyword,        // 可能是空字符串
  rule_type: this.searchForm.type,         // 可能是空字符串
  rule_status: this.searchForm.status,     // 可能是空字符串
  skip: (this.pagination.page - 1) * this.pagination.limit,
  limit: this.pagination.limit
}

// 修复后：只传递非空参数
const params = {
  skip: (this.pagination.page - 1) * this.pagination.limit,
  limit: this.pagination.limit
}

// 只有当值不为空时才添加到参数中
if (this.searchForm.keyword) {
  params.keyword = this.searchForm.keyword
}
if (this.searchForm.type) {
  params.rule_type = this.searchForm.type
}
if (this.searchForm.status) {
  params.rule_status = this.searchForm.status
}
```

## 修复的文件

1. `src/views/coupon/coupon-distribution-rule.vue` - 主管理页面
2. `src/views/coupon/coupon-distribution-rule-test.vue` - 测试页面
3. `src/api/coupon.js` - API配置（添加了新的接口）
4. `src/router/index.js` - 路由配置

## 测试步骤

### 1. 基本功能测试

1. 访问 `/coupon/distribution-rule` 页面
2. 检查页面是否正常加载
3. 查看浏览器控制台是否有错误信息

### 2. API调试测试

1. 访问 `/coupon/debug-api-test` 页面
2. 点击各个测试按钮
3. 查看API响应结果

### 3. 功能测试

1. 测试搜索功能
2. 测试新增规则
3. 测试编辑规则
4. 测试删除规则
5. 测试状态切换

## 当前状态

- ✅ 修复了导入错误
- ✅ 修复了API调用方式
- ✅ 统一了异步调用模式
- ✅ 添加了调试页面
- ⏳ 需要测试后端API是否正常工作

## 下一步

1. 确认后端API `/coupon/distribution-rules/` 是否正常工作
2. 测试前端页面功能
3. 根据测试结果进行进一步调整

## 常见问题

### Q: 页面加载时仍然报错怎么办？

A: 检查以下几点：
1. 确认后端服务是否启动
2. 确认API路径是否正确
3. 查看网络请求是否成功
4. 检查浏览器控制台的详细错误信息

### Q: API调用返回404错误怎么办？

A: 
1. 确认后端是否实现了对应的API接口
2. 检查API路径是否正确
3. 确认路由配置是否正确

### Q: 数据格式不匹配怎么办？

A:
1. 检查前端发送的数据格式
2. 检查后端期望的数据格式
3. 调整数据转换逻辑

## 联系方式

如果遇到其他问题，请查看：
1. 浏览器控制台错误信息
2. 网络请求详情
3. 后端日志信息
