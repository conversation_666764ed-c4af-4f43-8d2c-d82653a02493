<template>
  <div class="batch-debug">
    <h1>优惠券批次调试页面</h1>
    
    <div class="test-section">
      <h2>批次数据测试</h2>
      <el-button @click="testAllBatches">获取所有批次</el-button>
      <el-button @click="testActiveBatches">获取激活批次</el-button>
      <el-button @click="testInactiveBatches">获取未激活批次</el-button>
      <el-button @click="testWithKeyword">按关键词搜索</el-button>
    </div>

    <div class="test-section">
      <h2>当前批次数据</h2>
      <p>批次数量: {{ batches.length }}</p>
      <el-table :data="batches" border style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="名称" width="200" />
        <el-table-column prop="status" label="状态" width="100" />
        <el-table-column prop="quantity" label="数量" width="100" />
        <el-table-column prop="coupon_id" label="优惠券ID" width="120" />
      </el-table>
    </div>

    <div class="test-section">
      <h2>API响应详情</h2>
      <pre>{{ apiResponse }}</pre>
    </div>
  </div>
</template>

<script>
import { requestApi } from '@/utils/request'

export default {
  name: 'BatchDebug',
  data() {
    return {
      batches: [],
      apiResponse: ''
    }
  },
  methods: {
    async testAllBatches() {
      this.apiResponse = '正在获取所有批次...'
      
      try {
        const response = await requestApi({
          name: 'getCouponBatchesBySearch',
          data: {
            page: 1,
            pageSize: 100
          }
        })
        
        console.log('所有批次响应:', response)
        this.apiResponse = JSON.stringify(response, null, 2)
        
        if (response && response.code === 200) {
          this.batches = response.data.list || []
          this.$message.success(`获取到 ${response.data.total} 个批次`)
        } else {
          this.$message.error('获取批次失败')
        }
      } catch (error) {
        console.error('获取所有批次失败:', error)
        this.apiResponse = `错误: ${error.message}`
        this.$message.error('API调用失败')
      }
    },

    async testActiveBatches() {
      this.apiResponse = '正在获取激活批次...'
      
      try {
        const response = await requestApi({
          name: 'getCouponBatchesBySearch',
          data: {
            status: 1,
            page: 1,
            pageSize: 100
          }
        })
        
        console.log('激活批次响应:', response)
        this.apiResponse = JSON.stringify(response, null, 2)
        
        if (response && response.code === 200) {
          this.batches = response.data.list || []
          this.$message.success(`获取到 ${response.data.total} 个激活批次`)
        } else {
          this.$message.error('获取激活批次失败')
        }
      } catch (error) {
        console.error('获取激活批次失败:', error)
        this.apiResponse = `错误: ${error.message}`
        this.$message.error('API调用失败')
      }
    },

    async testInactiveBatches() {
      this.apiResponse = '正在获取未激活批次...'
      
      try {
        const response = await requestApi({
          name: 'getCouponBatchesBySearch',
          data: {
            status: 0,
            page: 1,
            pageSize: 100
          }
        })
        
        console.log('未激活批次响应:', response)
        this.apiResponse = JSON.stringify(response, null, 2)
        
        if (response && response.code === 200) {
          this.batches = response.data.list || []
          this.$message.success(`获取到 ${response.data.total} 个未激活批次`)
        } else {
          this.$message.error('获取未激活批次失败')
        }
      } catch (error) {
        console.error('获取未激活批次失败:', error)
        this.apiResponse = `错误: ${error.message}`
        this.$message.error('API调用失败')
      }
    },

    async testWithKeyword() {
      this.apiResponse = '正在按关键词搜索...'
      
      try {
        const response = await requestApi({
          name: 'getCouponBatchesBySearch',
          data: {
            keyword: 'test',
            page: 1,
            pageSize: 100
          }
        })
        
        console.log('关键词搜索响应:', response)
        this.apiResponse = JSON.stringify(response, null, 2)
        
        if (response && response.code === 200) {
          this.batches = response.data.list || []
          this.$message.success(`搜索到 ${response.data.total} 个批次`)
        } else {
          this.$message.error('搜索失败')
        }
      } catch (error) {
        console.error('关键词搜索失败:', error)
        this.apiResponse = `错误: ${error.message}`
        this.$message.error('API调用失败')
      }
    }
  },
  
  mounted() {
    // 页面加载时自动获取所有批次
    this.testAllBatches()
  }
}
</script>

<style scoped>
.batch-debug {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
