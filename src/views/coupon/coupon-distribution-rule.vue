<template>
  <div class="coupon-distribution-rule">
    <h1>发券规则管理</h1>

    <!-- 头部菜单 -->
    <div class="search-bar">
      <div class="search-container">
        <div class="input-group">
          <el-input
            v-model="searchForm.keyword"
            placeholder="规则名称"
            size="mini"
            class="filter-item"
          />
        </div>
        <div class="input-group">
          <el-select
            v-model="searchForm.type"
            placeholder="规则类型"
            size="mini"
            clearable
            class="filter-item"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="input-group">
          <el-select
            v-model="searchForm.status"
            placeholder="规则状态"
            size="mini"
            clearable
            class="filter-item"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <el-button type="primary" size="mini" @click="handleSearch">
          查询
        </el-button>
        <el-button type="primary" size="mini" @click="resetSearch">
          重置
        </el-button>
      </div>
      <div class="left-buttons">
        <el-button type="primary" size="mini" @click="handleAdd">
          新增规则
        </el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="规则名称" min-width="150" />
      <el-table-column prop="description" label="规则描述" min-width="200" />
      <el-table-column prop="type" label="规则类型" width="150">
        <template slot-scope="scope">
          <el-tag :type="getTypeTagType(scope.row.type)">
            {{ getTypeLabel(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
            {{ scope.row.status === 'active' ? '生效' : '未生效' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="execution_time" label="执行时间" width="150" />
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            size="mini"
            :type="scope.row.status === 'active' ? 'warning' : 'success'"
            @click="handleStatusChange(scope.row)"
          >
            {{ scope.row.status === 'active' ? '禁用' : '启用' }}
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.limit"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="ruleRules"
        label-width="120px"
      >
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="规则描述" prop="description">
          <el-input
            v-model="ruleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入规则描述"
          />
        </el-form-item>
        <el-form-item label="规则类型" prop="type">
          <el-select v-model="ruleForm.type" placeholder="请选择规则类型" style="width: 100%">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="ruleForm.type === 'user_feature' || ruleForm.type === 'user_feature_and_behavior'"
          label="用户特征函数"
          prop="user_feature_function"
        >
          <el-input
            v-model="ruleForm.user_feature_function"
            type="textarea"
            :rows="4"
            placeholder="请输入用户特征函数"
          />
        </el-form-item>
        <el-form-item
          v-if="ruleForm.type === 'user_behavior' || ruleForm.type === 'user_feature_and_behavior'"
          label="用户行为类型"
          prop="user_behavior_types"
        >
          <el-select
            v-model="ruleForm.user_behavior_types"
            multiple
            placeholder="请选择用户行为类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in behaviorOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发放内容" prop="distribution_content">
          <div class="distribution-content">
            <div
              v-for="(item, index) in ruleForm.distribution_content"
              :key="index"
              class="content-item"
            >
              <el-select
                v-model="item.coupon_batch_id"
                placeholder="选择优惠券批次"
                style="width: 200px; margin-right: 10px"
              >
                <el-option
                  v-for="batch in couponBatches"
                  :key="batch.id"
                  :label="batch.name"
                  :value="batch.id"
                />
              </el-select>
              <el-input-number
                v-model="item.quantity"
                :min="1"
                placeholder="数量"
                style="width: 120px; margin-right: 10px"
              />
              <el-button
                type="danger"
                size="mini"
                @click="removeDistributionContent(index)"
              >
                删除
              </el-button>
            </div>
            <el-button type="primary" size="mini" @click="addDistributionContent">
              添加发放内容
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="执行时间" prop="execution_time">
          <el-input
            v-model="ruleForm.execution_time"
            placeholder="请输入cron表达式，如：0 0 12 * * ?"
          />
        </el-form-item>
        <el-form-item label="规则状态" prop="status">
          <el-radio-group v-model="ruleForm.status">
            <el-radio label="active">生效</el-radio>
            <el-radio label="inactive">未生效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from '@/utils/request'

export default {
  name: 'CouponDistributionRule',
  data() {
    return {
      listLoading: false,
      tableData: [],
      selectedRows: [],
      searchForm: {
        keyword: '',
        type: '',
        status: ''
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      dialogVisible: false,
      dialogTitle: '新增规则',
      isEdit: false,
      editId: null,
      ruleForm: {
        name: '',
        description: '',
        type: '',
        user_feature_function: '',
        user_behavior_types: [],
        distribution_content: [],
        execution_time: '',
        status: 'active'
      },
      ruleRules: {
        name: [
          { required: true, message: '请输入规则名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择规则类型', trigger: 'change' }
        ],
        distribution_content: [
          { required: true, message: '请添加发放内容', trigger: 'change' }
        ]
      },
      typeOptions: [
        { value: 'user_feature', label: '用户特征' },
        { value: 'user_behavior', label: '用户行为' },
        { value: 'user_feature_and_behavior', label: '用户特征与行为' }
      ],
      statusOptions: [
        { value: 'active', label: '生效' },
        { value: 'inactive', label: '未生效' }
      ],
      behaviorOptions: [
        { value: 'created', label: '创建' },
        { value: 'updated', label: '更新' },
        { value: 'deleted', label: '删除' },
        { value: 'login', label: '登录' },
        { value: 'logout', label: '登出' },
        { value: 'password_changed', label: '密码修改' },
        { value: 'viewed', label: '浏览' },
        { value: 'shared', label: '分享' },
        { value: 'registered', label: '注册' },
        { value: 'ordered', label: '下单' },
        { value: 'paid', label: '支付' },
        { value: 'used', label: '使用' },
        { value: 'cancelled', label: '取消' },
        { value: 'refunded', label: '退款' },
        { value: 'verified', label: '验证' }
      ],
      couponBatches: []
    }
  },
  computed: {
    
  },
  mounted() {
    this.getList()
    this.getCouponBatches()
  },
  methods: {
    // 获取列表数据
    getList() {
      this.listLoading = true
      const params = {
        skip: (this.pagination.page - 1) * this.pagination.limit,
        limit: this.pagination.limit
      }

      // 只有当值不为空时才添加到参数中
      if (this.searchForm.keyword) {
        params.keyword = this.searchForm.keyword
      }
      if (this.searchForm.type) {
        params.rule_type = this.searchForm.type
      }
      if (this.searchForm.status) {
        params.rule_status = this.searchForm.status
      }

      console.log('调用发券规则列表API，参数:', params)
      requestApi({
        name: 'getDistributionRules',
        data: params
      })
        .then((response) => {
          this.listLoading = false
          console.log('发券规则列表API响应:', response)
          if (response && response.code === 200) {
            this.tableData = response.data.items || []
            this.pagination.total = response.data.total || 0
          } else {
            this.$message.error(response.message || '获取发券规则列表失败')
          }
        })
        .catch((error) => {
          this.listLoading = false
          console.error('获取发券规则列表失败', error)
          this.$message.error('获取数据失败')
        })
    },

    // 获取优惠券批次列表
    getCouponBatches() {
      console.log('开始获取优惠券批次列表...')
      requestApi({
        name: 'getCouponBatchesBySearch',
        data: {
          keyword: '',
          status: 'active',
          page: 1,
          page_size: 1000
        }
      })
        .then((response) => {
          console.log('优惠券批次API响应:', response)
          if (response && response.code === 200) {
            console.log('批次数据:', response.data)
            console.log('批次列表:', response.data.list)
            this.couponBatches = response.data.list || []
            console.log('设置后的couponBatches:', this.couponBatches)

            // 检查数据结构
            if (this.couponBatches.length > 0) {
              console.log('第一个批次数据结构:', this.couponBatches[0])
            }
          } else {
            console.error('API返回错误:', response)
            this.$message.error(response.message || '获取优惠券批次失败')
          }
        })
        .catch((error) => {
          console.error('获取优惠券批次失败:', error)
          this.$message.error('获取优惠券批次失败')
        })
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.getList()
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        keyword: '',
        type: '',
        status: ''
      }
      this.pagination.page = 1
      this.getList()
    },

    // 新增
    handleAdd() {
      this.dialogTitle = '新增规则'
      this.isEdit = false
      this.editId = null
      this.resetForm()
      this.dialogVisible = true
    },

    // 编辑
    async handleEdit(row) {
      this.dialogTitle = '编辑规则'
      this.isEdit = true
      this.editId = row.id

      try {
        const response = await requestApi({
          name: 'getDistributionRule',
          data: { id: row.id }
        })

        if (response.code === 200) {
          const data = response.data
          this.ruleForm = {
            name: data.name,
            description: data.description || '',
            type: data.type,
            user_feature_function: data.user_feature_function || '',
            user_behavior_types: data.user_behavior_types || [],
            distribution_content: data.distribution_content || [],
            execution_time: data.execution_time || '',
            status: data.status
          }
          this.dialogVisible = true
        }
      } catch (error) {
        this.$message.error('获取规则详情失败')
        console.error(error)
      }
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确定要删除这条规则吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await requestApi({
            name: 'deleteDistributionRule',
            data: { id: row.id }
          })

          if (response.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          }
        } catch (error) {
          this.$message.error('删除失败')
          console.error(error)
        }
      })
    },

    // 状态变更
    handleStatusChange(row) {
      const newStatus = row.status === 'active' ? 'inactive' : 'active'
      const statusText = newStatus === 'active' ? '启用' : '禁用'

      this.$confirm(`确定要${statusText}这条规则吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await requestApi({
            name: 'updateDistributionRuleStatus',
            data: { id: row.id, status: newStatus }
          })

          if (response.code === 200) {
            this.$message.success(`${statusText}成功`)
            this.getList()
          }
        } catch (error) {
          this.$message.error(`${statusText}失败`)
          console.error(error)
        }
      })
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.pagination.limit = size
      this.pagination.page = 1
      this.getList()
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.pagination.page = page
      this.getList()
    },

    // 提交表单
    handleSubmit() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          try {
            const data = { ...this.ruleForm }

            // 验证必填字段
            if (data.type === 'user_feature' || data.type === 'user_feature_and_behavior') {
              if (!data.user_feature_function) {
                this.$message.error('用户特征函数不能为空')
                return
              }
            }

            if (data.type === 'user_behavior' || data.type === 'user_feature_and_behavior') {
              if (!data.user_behavior_types || data.user_behavior_types.length === 0) {
                this.$message.error('用户行为类型不能为空')
                return
              }
            }

            if (!data.distribution_content || data.distribution_content.length === 0) {
              this.$message.error('发放内容不能为空')
              return
            }

            let response
            if (this.isEdit) {
              response = await requestApi({
                name: 'updateDistributionRule',
                data: { ...data, id: this.editId }
              })
            } else {
              response = await requestApi({
                name: 'addDistributionRule',
                data
              })
            }

            if (response.code === 200) {
              this.$message.success(this.isEdit ? '更新成功' : '创建成功')
              this.dialogVisible = false
              this.getList()
            }
          } catch (error) {
            this.$message.error(this.isEdit ? '更新失败' : '创建失败')
            console.error(error)
          }
        }
      })
    },

    // 对话框关闭
    handleDialogClose() {
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      this.ruleForm = {
        name: '',
        description: '',
        type: '',
        user_feature_function: '',
        user_behavior_types: [],
        distribution_content: [],
        execution_time: '',
        status: 'active'
      }
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.clearValidate()
      }
    },

    // 添加发放内容
    addDistributionContent() {
      this.ruleForm.distribution_content.push({
        coupon_batch_id: '',
        quantity: 1
      })
    },

    // 删除发放内容
    removeDistributionContent(index) {
      this.ruleForm.distribution_content.splice(index, 1)
    },

    // 获取类型标签类型
    getTypeTagType(type) {
      const typeMap = {
        'user_feature': 'primary',
        'user_behavior': 'success',
        'user_feature_and_behavior': 'warning'
      }
      return typeMap[type] || 'info'
    },

    // 获取类型标签文本
    getTypeLabel(type) {
      const typeMap = {
        'user_feature': '用户特征',
        'user_behavior': '用户行为',
        'user_feature_and_behavior': '用户特征与行为'
      }
      return typeMap[type] || type
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      const date = new Date(dateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.coupon-distribution-rule {
  padding: 20px;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.input-group {
  display: flex;
  align-items: center;
}

.filter-item {
  width: 200px;
}

.left-buttons {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.distribution-content {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.content-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.content-item:last-child {
  margin-bottom: 0;
}

.dialog-footer {
  text-align: right;
}

.el-table {
  margin-top: 0;
}

.el-tag {
  margin-right: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
    gap: 15px;
  }

  .search-container {
    flex-wrap: wrap;
    justify-content: center;
  }

  .filter-item {
    width: 150px;
  }

  .content-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
