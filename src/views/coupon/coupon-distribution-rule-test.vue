<template>
  <div class="test-page">
    <h1>发券规则管理测试页面</h1>
    
    <div class="test-section">
      <h2>API 测试</h2>
      <el-button @click="testGetList">测试获取列表</el-button>
      <el-button @click="testCreateRule">测试创建规则</el-button>
      <el-button @click="testGetBatches">测试获取批次</el-button>
    </div>

    <div class="test-section">
      <h2>枚举值测试</h2>
      <div>
        <h3>规则类型选项:</h3>
        <ul>
          <li v-for="item in typeOptions" :key="item.value">
            {{ item.label }} ({{ item.value }})
          </li>
        </ul>
      </div>
      
      <div>
        <h3>用户行为类型选项:</h3>
        <ul>
          <li v-for="item in behaviorOptions" :key="item.value">
            {{ item.label }} ({{ item.value }})
          </li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h2>测试结果</h2>
      <pre>{{ testResult }}</pre>
    </div>
  </div>
</template>

<script>
import { requestApi } from '@/utils/request'

export default {
  name: 'CouponDistributionRuleTest',
  data() {
    return {
      testResult: '',
      typeOptions: [
        { value: 'user_feature', label: '用户特征' },
        { value: 'user_behavior', label: '用户行为' },
        { value: 'user_feature_and_behavior', label: '用户特征与行为' }
      ],
      behaviorOptions: [
        { value: 'created', label: '创建' },
        { value: 'updated', label: '更新' },
        { value: 'deleted', label: '删除' },
        { value: 'login', label: '登录' },
        { value: 'logout', label: '登出' },
        { value: 'password_changed', label: '密码修改' },
        { value: 'viewed', label: '浏览' },
        { value: 'shared', label: '分享' },
        { value: 'registered', label: '注册' },
        { value: 'ordered', label: '下单' },
        { value: 'paid', label: '支付' },
        { value: 'used', label: '使用' },
        { value: 'cancelled', label: '取消' },
        { value: 'refunded', label: '退款' },
        { value: 'verified', label: '验证' }
      ]
    }
  },
  methods: {
    async testGetList() {
      try {
        this.testResult = '正在测试获取列表...'
        
        const response = await requestApi({
          name: 'getDistributionRules',
          data: {
            skip: 0,
            limit: 10
          }
        })
        
        this.testResult = JSON.stringify(response, null, 2)
        this.$message.success('获取列表测试成功')
      } catch (error) {
        this.testResult = `获取列表测试失败: ${error.message}`
        this.$message.error('获取列表测试失败')
        console.error(error)
      }
    },

    async testCreateRule() {
      try {
        this.testResult = '正在测试创建规则...'
        
        const testData = {
          name: '测试发券规则',
          description: '这是一个测试用的发券规则',
          type: 'user_behavior',
          user_behavior_types: ['registered'],
          distribution_content: [
            {
              coupon_batch_id: 1,
              quantity: 1
            }
          ],
          execution_time: '0 0 12 * * ?',
          status: 'active'
        }
        
        const response = await requestApi({
          name: 'addDistributionRule',
          data: testData
        })
        
        this.testResult = JSON.stringify(response, null, 2)
        this.$message.success('创建规则测试成功')
      } catch (error) {
        this.testResult = `创建规则测试失败: ${error.message}`
        this.$message.error('创建规则测试失败')
        console.error(error)
      }
    },

    async testGetBatches() {
      try {
        this.testResult = '正在测试获取批次...'
        
        const response = await requestApi({
          name: 'getCouponBatchesBySearch',
          data: {
            keyword: '',
            status: 'active',
            page: 1,
            page_size: 10
          }
        })
        
        this.testResult = JSON.stringify(response, null, 2)
        this.$message.success('获取批次测试成功')
      } catch (error) {
        this.testResult = `获取批次测试失败: ${error.message}`
        this.$message.error('获取批次测试失败')
        console.error(error)
      }
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

.test-section h3 {
  color: #666;
  margin-bottom: 10px;
}

.test-section ul {
  margin: 0;
  padding-left: 20px;
}

.test-section li {
  margin-bottom: 5px;
}

pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
}

.el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
