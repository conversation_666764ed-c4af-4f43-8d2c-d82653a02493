<template>
  <div class="simple-test">
    <h1>简单API测试</h1>
    
    <div class="test-section">
      <h2>测试发券规则API</h2>
      <el-button @click="testBasicCall">测试基本调用</el-button>
      <el-button @click="testWithParams">测试带参数调用</el-button>
    </div>

    <div class="test-section">
      <h2>测试结果</h2>
      <div>
        <h3>请求URL:</h3>
        <pre>{{ requestUrl }}</pre>
      </div>
      <div>
        <h3>响应结果:</h3>
        <pre>{{ testResult }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import { requestApi } from '@/utils/request'

export default {
  name: 'SimpleTest',
  data() {
    return {
      testResult: '',
      requestUrl: ''
    }
  },
  methods: {
    testBasicCall() {
      this.testResult = '正在测试基本调用...'
      this.requestUrl = ''
      
      const params = {
        skip: 0,
        limit: 10
      }
      
      console.log('发送参数:', params)
      this.requestUrl = `GET /api/v1/coupon/distribution-rules/?skip=0&limit=10`
      
      requestApi({
        name: 'getDistributionRules',
        data: params
      })
        .then((response) => {
          console.log('API响应:', response)
          this.testResult = JSON.stringify(response, null, 2)
          this.$message.success('基本调用测试成功')
        })
        .catch((error) => {
          console.error('API错误:', error)
          this.testResult = `错误: ${error.message || error}`
          this.$message.error('基本调用测试失败')
        })
    },

    testWithParams() {
      this.testResult = '正在测试带参数调用...'
      this.requestUrl = ''
      
      const params = {
        keyword: 'test',
        rule_type: 'user_behavior',
        rule_status: 'active',
        skip: 0,
        limit: 10
      }
      
      console.log('发送参数:', params)
      this.requestUrl = `GET /api/v1/coupon/distribution-rules/?keyword=test&rule_type=user_behavior&rule_status=active&skip=0&limit=10`
      
      requestApi({
        name: 'getDistributionRules',
        data: params
      })
        .then((response) => {
          console.log('API响应:', response)
          this.testResult = JSON.stringify(response, null, 2)
          this.$message.success('带参数调用测试成功')
        })
        .catch((error) => {
          console.error('API错误:', error)
          this.testResult = `错误: ${error.message || error}`
          this.$message.error('带参数调用测试失败')
        })
    }
  }
}
</script>

<style scoped>
.simple-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

.test-section h3 {
  color: #666;
  margin-bottom: 10px;
}

pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
