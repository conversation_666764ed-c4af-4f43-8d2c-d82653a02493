<template>
  <div class="debug-test">
    <h1>API调试测试</h1>
    
    <div class="test-section">
      <h2>测试 requestApi 函数</h2>
      <el-button @click="testRequestApi">测试基本调用</el-button>
      <el-button @click="testExistingApi">测试现有API</el-button>
      <el-button @click="testDistributionRules">测试发券规则API</el-button>
    </div>

    <div class="test-section">
      <h2>测试结果</h2>
      <pre>{{ testResult }}</pre>
    </div>
  </div>
</template>

<script>
import { requestApi } from '@/utils/request'

export default {
  name: 'DebugApiTest',
  data() {
    return {
      testResult: ''
    }
  },
  methods: {
    async testRequestApi() {
      try {
        this.testResult = '测试 requestApi 函数...'
        console.log('requestApi function:', requestApi)
        this.testResult = `requestApi 函数类型: ${typeof requestApi}`
      } catch (error) {
        this.testResult = `测试失败: ${error.message}`
        console.error(error)
      }
    },

    async testExistingApi() {
      try {
        this.testResult = '测试现有API...'
        
        const response = await requestApi({
          name: 'getCouponBatchesBySearch',
          data: {
            keyword: '',
            page: 1,
            page_size: 10
          }
        })
        
        this.testResult = JSON.stringify(response, null, 2)
        this.$message.success('现有API测试成功')
      } catch (error) {
        this.testResult = `现有API测试失败: ${error.message}`
        this.$message.error('现有API测试失败')
        console.error(error)
      }
    },

    async testDistributionRules() {
      try {
        this.testResult = '测试发券规则API...'
        
        const response = await requestApi({
          name: 'getDistributionRules',
          data: {
            skip: 0,
            limit: 10
          }
        })
        
        this.testResult = JSON.stringify(response, null, 2)
        this.$message.success('发券规则API测试成功')
      } catch (error) {
        this.testResult = `发券规则API测试失败: ${error.message}`
        this.$message.error('发券规则API测试失败')
        console.error(error)
      }
    }
  }
}
</script>

<style scoped>
.debug-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
}

.el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
