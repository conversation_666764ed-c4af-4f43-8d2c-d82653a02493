# 优惠券批次下拉框为空问题修复

## 问题描述

在发券规则管理的新增/编辑对话框中，"发放内容"部分的优惠券批次下拉框显示为空，无法选择任何批次。

## 问题分析

经过排查，发现了以下几个问题：

### 1. API参数名不匹配

**问题**：后端API期望的参数名是 `pageSize`，但前端发送的是 `page_size`

**后端代码**：
```python
page_size = body.get("pageSize", 10)  # 期望 pageSize
```

**前端代码（修复前）**：
```javascript
data: {
  keyword: '',
  status: 'active',
  page: 1,
  page_size: 1000  // 错误：应该是 pageSize
}
```

**修复**：
```javascript
data: {
  keyword: '',
  status: 'active',
  page: 1,
  pageSize: 1000  // 正确
}
```

### 2. 缺少调试信息

**问题**：无法确定API调用是否成功，数据是否正确返回

**修复**：添加了详细的调试日志
```javascript
getCouponBatches() {
  console.log('开始获取优惠券批次列表...')
  // ... API调用
  .then((response) => {
    console.log('优惠券批次API响应:', response)
    console.log('批次数据:', response.data)
    console.log('批次列表:', response.data.list)
    // ... 处理数据
    console.log('设置后的couponBatches:', this.couponBatches)
    
    if (this.couponBatches.length > 0) {
      console.log('第一个批次数据结构:', this.couponBatches[0])
    }
  })
}
```

### 3. 用户体验问题

**问题**：用户需要手动点击"添加发放内容"才能看到下拉框

**修复**：在打开新增对话框时自动添加一个发放内容项
```javascript
handleAdd() {
  // ... 其他代码
  
  // 确保有至少一个发放内容项
  if (this.ruleForm.distribution_content.length === 0) {
    this.addDistributionContent()
  }
  
  // 重新获取批次数据以确保数据是最新的
  this.getCouponBatches()
  
  this.dialogVisible = true
}
```

### 4. 添加调试功能

**新增**：添加了"调试批次"按钮，方便开发者检查数据状态
```javascript
debugBatches() {
  console.log('=== 调试批次数据 ===')
  console.log('当前 couponBatches:', this.couponBatches)
  console.log('couponBatches 长度:', this.couponBatches.length)
  
  if (this.couponBatches.length > 0) {
    console.log('第一个批次:', this.couponBatches[0])
  }
  
  // 重新获取批次数据
  this.getCouponBatches()
  
  this.$message.info(`当前有 ${this.couponBatches.length} 个批次`)
}
```

## 修复的文件

1. `src/views/coupon/coupon-distribution-rule.vue` - 主要修复
2. `src/views/coupon/simple-test.vue` - 测试页面修复

## 测试步骤

1. **访问主页面**：`/coupon/distribution-rule`
2. **点击调试按钮**：点击"调试批次"按钮，查看控制台输出
3. **检查API调用**：确认批次API返回正确数据
4. **测试新增功能**：
   - 点击"新增规则"
   - 检查是否自动添加了发放内容项
   - 检查下拉框是否显示批次选项
5. **查看控制台**：确认没有错误信息

## 预期结果

修复后应该看到：
- 批次API成功返回数据
- 控制台显示批次列表信息
- 新增对话框中的下拉框显示可用的优惠券批次
- 用户可以正常选择批次

## 如果问题仍然存在

如果修复后问题仍然存在，请检查：

1. **后端服务状态**：确认后端服务正常运行
2. **数据库数据**：确认数据库中确实存在优惠券批次数据
3. **网络请求**：查看浏览器网络面板，确认API请求成功
4. **控制台错误**：查看是否有JavaScript错误
5. **API响应**：检查API返回的数据格式是否正确

## 联系信息

如果需要进一步协助，请提供：
- 浏览器控制台的完整输出
- 网络请求的详细信息
- 后端日志（如果可访问）
