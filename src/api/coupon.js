const base = ''
export default {
  // ====================== 优惠券管理接口 ======================
  //根据关键字搜索价格策略列表
  getCouponsBySearch: {
    url: base + '/coupon/search/',
    method: 'POST'
  },

  getCouponsByName: {
    url: base + '/coupon/search/name',
    method: 'GET'
  },

  // 获取价格策略明细
  getCoupon: {
    url: base + '/coupon/view/:{id}',
    method: 'GET'
  },

  // 新增价格策略
  addCoupon: {
    url: base + '/coupon/create/',
    method: 'POST'
  },

  // 删除价格策略
  deleteCoupon: {
    url: base + '/coupon/delete/:{id}',
    method: 'DELETE'
  },

  // 更新价格策略
  updateCoupon: {
    url: base + '/coupon/update/:{id}',
    method: 'PUT'
  },

  // 更新价格策略状态（启用/禁用）
  updateCouponStatus: {
    url: base + '/coupon/status/:{id}',
    method: 'POST'
  },

  // 获取优惠券使用记录
  getCouponUsagesBySearch: {
    url: base + '/coupon/coupon-usage-records/search',
    method: 'POST'
  },

  // ====================== 发券规则管理接口 ======================
  // 获取发券规则列表
  getDistributionRules: {
    url: base + '/coupon/distribution-rules/',
    method: 'GET'
  },

  // 获取发券规则详情
  getDistributionRule: {
    url: base + '/coupon/distribution-rules/:{id}',
    method: 'GET'
  },

  // 新增发券规则
  addDistributionRule: {
    url: base + '/coupon/distribution-rules/',
    method: 'POST'
  },

  // 更新发券规则
  updateDistributionRule: {
    url: base + '/coupon/distribution-rules/:{id}',
    method: 'PUT'
  },

  // 删除发券规则
  deleteDistributionRule: {
    url: base + '/coupon/distribution-rules/:{id}',
    method: 'DELETE'
  },

  // 更新发券规则状态
  updateDistributionRuleStatus: {
    url: base + '/coupon/distribution-rules/:{id}/status',
    method: 'PUT'
  },

  // 获取优惠券使用记录
  distributeCoupons: {
    url: base + '/coupon/coupon-usage-records/batch',
    method: 'POST'
  },

  // ====================== 优惠券批次管理接口 ======================
  // 搜索优惠券批次列表
  getCouponBatchesBySearch: {
    url: base + '/coupon/batches/search',
    method: 'POST'
  },

  // 获取优惠券批次详情
  getCouponBatch: {
    url: base + '/coupon/batches/view/:{id}',
    method: 'GET'
  },

  // 新增优惠券批次
  addCouponBatch: {
    url: base + '/coupon/batches/create',
    method: 'POST'
  },

  // 更新优惠券批次
  updateCouponBatch: {
    url: base + '/coupon/batches/update/:{id}',
    method: 'PUT'
  },

  // 删除优惠券批次
  deleteCouponBatch: {
    url: base + '/coupon/batches/delete/:{id}',
    method: 'DELETE'
  },

  // 更新优惠券批次状态
  updateCouponBatchStatus: {
    url: base + '/coupon/batches/status/:{id}',
    method: 'POST'
  },

  // 根据优惠券ID获取批次列表
  getCouponBatchesByCouponId: {
    url: base + '/coupon/batches/coupon/:{coupon_id}',
    method: 'GET'
  },

}
