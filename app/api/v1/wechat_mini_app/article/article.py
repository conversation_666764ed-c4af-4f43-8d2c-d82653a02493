from typing import Dict, Any, Optional

from fastapi import Depends, APIRouter, HTTPException, Header
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.core.config import settings
from app.dao.content import article_dao
from app.schemas.content import ArticleInDB
from app.events import UserEvent
from app.events.models import UserEventAction
from app.events.deps import EventBusDep
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.utils.logger import logger

router = APIRouter()


@router.get("/published/articles", response_model=Dict[str, Any])
async def get_published_articles(
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取所有已发布的文章
    按照sort_order升序排列
    """
    try:
        articles = article_dao.get_published_articles(session=db)
        # 将 SQLAlchemy 模型转换为 Pydantic 模型并添加 BASE_URL
        articles_data = []
        for article in articles:
            article_dict = ArticleInDB.model_validate(article).model_dump()
            # 为图片字段添加 BASE_URL
            if article_dict.get('image'):
                article_dict['image'] = settings.BASE_URL + article_dict['image']
            if article_dict.get('thumbnail'):
                article_dict['thumbnail'] = settings.BASE_URL + article_dict['thumbnail']
            if article_dict.get('ad_image'):
                article_dict['ad_image'] = settings.BASE_URL + article_dict['ad_image']
            articles_data.append(article_dict)
        
        return {
            "status": 200,
            "message": "获取已发布文章列表成功",
            "data": articles_data
        }
    except Exception as e:
        return {
            "status": 500,
            "message": f"获取已发布文章列表失败：{str(e)}",
            "data": None
        }


@router.get("/published/{article_id}", response_model=Dict[str, Any])
async def get_published_article(
        article_id: int,
        event_bus: EventBusDep,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    根据ID获取已发布的文章
    """
    try:
        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "code": 401}
            )

        article = article_dao.get_published_article(session=db, article_id=article_id)
        if not article:
            return {
                "status": 404,
                "message": "文章不存在或未发布",
                "data": None
            }
        # 将 SQLAlchemy 模型转换为 Pydantic 模型并添加 BASE_URL
        article_dict = ArticleInDB.model_validate(article).model_dump()
        # 为图片字段添加 BASE_URL
        if article_dict.get('image'):
            article_dict['image'] = settings.BASE_URL + article_dict['image']
        if article_dict.get('thumbnail'):
            article_dict['thumbnail'] = settings.BASE_URL + article_dict['thumbnail']
        if article_dict.get('ad_image'):
            article_dict['ad_image'] = settings.BASE_URL + article_dict['ad_image']

        try:
            action = "viewed"
            user_id = user.id  # todo: 获取用户ID
            user_type = user.type  # todo: 获取用户类型
            username = user.username  # todo: 获取用户名
            additional_data = {
                "user": user,
                "article_id": article_id,
                "article": article_dict
            }
            event = UserEvent(
                action=UserEventAction(action),
                user_id=user_id,
                user_type=user_type.value,
                username=username,
                additional_data=additional_data or None,
                source="get_published_article"
            )

            await event_bus.publish(event)
            logger.info(f"用户浏览文章事件已发布: {article.id}")
        except Exception as e:
            logger.error(f"用户浏览文章事件发布失败: {e}")

        message = ""

        

        return {
            "status": 200,
            "message": "获取文章详情成功",
            "data": {
                "article": article_dict,
                "message": message
            }
        }
    except Exception as e:
        return {
            "status": 500,
            "message": f"获取文章详情失败：{str(e)}",
            "data": None
        }
